import MDEditor from '@uiw/react-md-editor';
import '@uiw/react-md-editor/markdown-editor.css';
import '@uiw/react-markdown-preview/markdown.css';

const RichTextEditor = ({ value, onChange, placeholder = "Write your content here..." }) => {
  return (
    <div className="rich-text-editor">
      <MDEditor
        value={value || ''}
        onChange={(val) => onChange(val || '')}
        preview="edit"
        hideToolbar={false}
        visibleDragBar={false}
        textareaProps={{
          placeholder,
          style: {
            fontSize: 14,
            lineHeight: 1.6,
            fontFamily: 'inherit'
          }
        }}
        height={300}
        data-color-mode="light"
      />

      <div className="editor-help">
        <p><strong>Markdown supported:</strong> **bold**, *italic*, [links](url), `code`, ```code blocks```, lists, and more!</p>
      </div>
      {/* Markdown Preview */}
      <div style={{ marginTop: 16 }}>
        <strong>Preview:</strong>
        <MDEditor.Markdown source={value || ''} style={{ background: "#fff", padding: 12, borderRadius: 4 }} />
      </div>
    </div>
  );
};

export default RichTextEditor;
