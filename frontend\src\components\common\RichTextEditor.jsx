import { useState } from 'react';

const RichTextEditor = ({ value, onChange, placeholder = "Write your content here..." }) => {
  const [isPreview, setIsPreview] = useState(false);

  const handleToolbarAction = (action) => {
    const textarea = document.querySelector('.rich-editor-textarea');
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = value.substring(start, end);
    let newText = value;

    switch (action) {
      case 'bold':
        newText = value.substring(0, start) + `**${selectedText || 'bold text'}**` + value.substring(end);
        break;
      case 'italic':
        newText = value.substring(0, start) + `*${selectedText || 'italic text'}*` + value.substring(end);
        break;
      case 'link':
        const url = prompt('Enter URL:');
        if (url) {
          newText = value.substring(0, start) + `[${selectedText || 'link text'}](${url})` + value.substring(end);
        }
        break;
      case 'code':
        newText = value.substring(0, start) + `\`${selectedText || 'code'}\`` + value.substring(end);
        break;
      case 'list':
        newText = value.substring(0, start) + `\n- ${selectedText || 'list item'}` + value.substring(end);
        break;
      default:
        break;
    }

    onChange(newText);
  };

  return (
    <div className="rich-text-editor">
      <div className="editor-toolbar">
        <button type="button" onClick={() => handleToolbarAction('bold')} title="Bold">
          <strong>B</strong>
        </button>
        <button type="button" onClick={() => handleToolbarAction('italic')} title="Italic">
          <em>I</em>
        </button>
        <button type="button" onClick={() => handleToolbarAction('link')} title="Link">
          🔗
        </button>
        <button type="button" onClick={() => handleToolbarAction('code')} title="Code">
          &lt;/&gt;
        </button>
        <button type="button" onClick={() => handleToolbarAction('list')} title="List">
          • List
        </button>
        <button
          type="button"
          onClick={() => setIsPreview(!isPreview)}
          title="Toggle Preview"
          className={isPreview ? 'active' : ''}
        >
          👁️ Preview
        </button>
      </div>

      {!isPreview ? (
        <textarea
          className="rich-editor-textarea"
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          rows={12}
        />
      ) : (
        <div className="editor-preview" dangerouslySetInnerHTML={{
          __html: (value || '').replace(/\n/g, '<br>')
        }} />
      )}

      <div className="editor-help">
        <p><strong>Markdown supported:</strong> **bold**, *italic*, [links](url), `code`, - lists</p>
      </div>
    </div>
  );
};

export default RichTextEditor;
