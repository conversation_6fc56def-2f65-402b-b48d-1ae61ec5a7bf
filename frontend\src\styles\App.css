/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  line-height: 1.6;
  color: #2c3e50;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding: 2rem 0;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Buttons */
.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  text-decoration: none;
  display: inline-block;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-danger:hover {
  background-color: #c82333;
}

.btn-logout {
  background-color: transparent;
  color: #007bff;
  border: 1px solid #007bff;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.btn-logout:hover {
  background-color: #007bff;
  color: white;
}

/* Loading and error states */
.loading {
  text-align: center;
  padding: 2rem;
  font-size: 1.125rem;
  color: #6c757d;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
  border: 1px solid #f5c6cb;
}

/* Navbar */
.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  padding: 0.75rem 0;
  position: sticky;
  top: 0;
  z-index: 1000;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo h1 {
  color: #007bff;
  text-decoration: none;
  font-size: 1.75rem;
  font-weight: 700;
}

.nav-logo {
  text-decoration: none;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-link {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.2s;
}

.nav-link:hover {
  color: #007bff;
}

.nav-user {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-greeting {
  color: #6c757d;
  font-size: 0.875rem;
}

.nav-auth {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Footer */
.footer {
  background-color: #343a40;
  color: white;
  text-align: center;
  padding: 2rem 0;
  margin-top: auto;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Forms */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 0.375rem;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
}

/* Authentication pages */
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 70vh;
  padding: 2rem 1rem;
}

.auth-card {
  background: white;
  padding: 2rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
}

.auth-card h2 {
  text-align: center;
  margin-bottom: 2rem;
  color: #333;
}

.auth-link {
  text-align: center;
  margin-top: 1.5rem;
  color: #6c757d;
}

.auth-link a {
  color: #007bff;
  text-decoration: none;
}

.auth-link a:hover {
  text-decoration: underline;
}

/* Posts */
.posts-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.posts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.posts-header h1 {
  color: #333;
  font-size: 2.5rem;
  font-weight: 700;
}

.no-posts {
  text-align: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.no-posts p {
  font-size: 1.125rem;
  color: #6c757d;
  margin-bottom: 2rem;
}

.posts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
}

.post-card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  transition: transform 0.2s, box-shadow 0.2s;
}

.post-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.post-header h2 {
  margin-bottom: 1rem;
}

.post-title-link {
  text-decoration: none;
  color: #333;
  font-size: 1.5rem;
  font-weight: 600;
}

.post-title-link:hover {
  color: #007bff;
}

.post-meta {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.post-author {
  font-weight: 500;
}

.post-content {
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.post-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
}

.comments-count {
  font-size: 0.875rem;
  color: #6c757d;
}

.read-more {
  color: #007bff;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
}

.read-more:hover {
  text-decoration: underline;
}

/* Post Detail */
.post-detail-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 1rem;
}

.post-detail {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
}

.post-detail .post-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.post-detail .post-meta {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.author-name {
  font-weight: 600;
  color: #333;
}

.author-username {
  color: #6c757d;
  font-size: 0.875rem;
}

.post-dates {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.post-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.post-detail .post-content {
  font-size: 1.125rem;
  line-height: 1.8;
  color: #333;
}

/* Comments */
.comments-section {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
}

.comments-section h3 {
  margin-bottom: 2rem;
  color: #333;
}

.comment-form {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #e9ecef;
}

.comments-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.comment {
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 0.375rem;
  border-left: 4px solid #007bff;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.comment-author {
  font-weight: 600;
  color: #333;
}

.comment-date {
  font-size: 0.875rem;
  color: #6c757d;
}

.comment-content {
  line-height: 1.6;
}

.no-comments {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 2rem;
}

/* Post Form */
.post-form-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 1rem;
}

.post-form-card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.post-form-card h2 {
  margin-bottom: 2rem;
  color: #333;
}

.back-link {
  text-align: center;
  margin-top: 2rem;
}

/* App Layout */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding: 2rem 0;
  background-color: #f8f9fa;
}

/* Navigation */
.nav-menu {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.nav-link {
  color: #6c757d;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.nav-link:hover {
  color: #007bff;
  background-color: #f8f9fa;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.nav-user-menu {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.notification-btn {
  position: relative;
  background: none;
  border: none;
  color: #6c757d;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.notification-btn:hover {
  color: #007bff;
  background-color: #f8f9fa;
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #dc3545;
  color: white;
  font-size: 0.75rem;
  padding: 0.125rem 0.375rem;
  border-radius: 50%;
  min-width: 1.25rem;
  text-align: center;
}

.user-dropdown {
  position: relative;
}

.user-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: #6c757d;
  font-weight: 500;
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.user-btn:hover {
  color: #007bff;
  background-color: #f8f9fa;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.user-avatar-initials {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 600;
}

.dropdown-content {
  display: none;
  position: absolute;
  right: 0;
  top: 100%;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  min-width: 200px;
  z-index: 1000;
}

.user-dropdown:hover .dropdown-content {
  display: block;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.75rem 1rem;
  border: none;
  background: none;
  color: #333;
  text-decoration: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

.nav-auth {
  display: flex;
  gap: 1rem;
}

/* Search and Filters */
.search-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  gap: 1rem;
}

.search-form {
  display: flex;
  gap: 0.5rem;
  flex: 1;
  max-width: 400px;
}

.search-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 0.375rem;
  font-size: 1rem;
}

.sort-options {
  display: flex;
  gap: 0.5rem;
}

.sort-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #ced4da;
  background: white;
  color: #6c757d;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s;
}

.sort-btn:hover {
  background-color: #f8f9fa;
}

.sort-btn.active {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

/* Questions List */
.questions-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.question-card {
  display: flex;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.question-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.95);
}

.question-stats {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-right: 1.5rem;
  min-width: 80px;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat.accepted {
  color: #28a745;
}

.stat-number {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
}

.stat.accepted .stat-number {
  color: #28a745;
}

.stat-label {
  font-size: 0.875rem;
  color: #6c757d;
}

.accepted-icon {
  margin-top: 0.25rem;
  color: #28a745;
}

.question-content {
  flex: 1;
}

.question-title {
  margin-bottom: 1rem;
}

.question-title a {
  color: #007bff;
  text-decoration: none;
  font-size: 1.25rem;
  font-weight: 600;
}

.question-title a:hover {
  text-decoration: underline;
}

.question-excerpt {
  color: #6c757d;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.question-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.tag {
  padding: 0.25rem 0.75rem;
  background-color: #007bff;
  color: white;
  text-decoration: none;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: opacity 0.2s;
}

.tag:hover {
  opacity: 0.8;
  color: white;
}

.question-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
  color: #6c757d;
}

.question-author {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.author-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

.author-name {
  font-weight: 500;
  color: #007bff;
}

.author-reputation {
  color: #6c757d;
  font-size: 0.75rem;
}

.question-time {
  color: #6c757d;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
}

.pagination-info {
  color: #6c757d;
  font-size: 0.875rem;
}

/* Error text */
.error-text {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
}

/* Rich Text Editor */
.rich-text-editor {
  position: relative;
  border: 1px solid #e9ecef;
  border-radius: 0.375rem;
  background: white;
}

.editor-toolbar {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
  border-radius: 0.375rem 0.375rem 0 0;
  flex-wrap: wrap;
}

.editor-toolbar button {
  padding: 0.5rem 0.75rem;
  border: 1px solid #ced4da;
  background: white;
  border-radius: 0.25rem;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s;
  min-width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.editor-toolbar button:hover {
  background: #e9ecef;
  border-color: #007bff;
}

.editor-toolbar button.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.toolbar-divider {
  width: 1px;
  height: 24px;
  background: #ced4da;
  margin: 0 0.25rem;
}

.rich-editor-textarea {
  width: 100%;
  min-height: 300px;
  padding: 1rem;
  border: none;
  outline: none;
  resize: vertical;
  font-family: inherit;
  font-size: 1rem;
  line-height: 1.6;
  border-radius: 0 0 0.375rem 0.375rem;
}

.editor-preview {
  min-height: 300px;
  padding: 1rem;
  border-radius: 0 0 0.375rem 0.375rem;
  background: white;
  line-height: 1.6;
  border: 1px solid #e9ecef;
  border-top: none;
}

.editor-preview h2 {
  color: #333;
  margin: 1rem 0 0.5rem 0;
  font-size: 1.5rem;
}

.editor-preview strong {
  font-weight: 600;
  color: #333;
}

.editor-preview em {
  font-style: italic;
  color: #555;
}

.editor-preview code {
  background: #f8f9fa;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  color: #e83e8c;
}

.editor-preview a {
  color: #007bff;
  text-decoration: none;
}

.editor-preview a:hover {
  text-decoration: underline;
}

.editor-preview li {
  margin: 0.25rem 0;
  list-style: none;
  padding-left: 1rem;
  position: relative;
}

.editor-preview li:before {
  content: '•';
  color: #007bff;
  position: absolute;
  left: 0;
}

.editor-help {
  margin-top: 0.5rem;
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-radius: 0.375rem;
  border: 1px solid #e9ecef;
}

.editor-help p {
  margin: 0;
  font-size: 0.875rem;
  color: #6c757d;
}

.emoji-picker {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  z-index: 1000;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.emoji-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
}

.emoji-btn:hover {
  background-color: #f8f9fa;
}

.close-emoji-picker {
  width: 100%;
  padding: 0.5rem;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
}

/* Tag Input */
.tag-input-container {
  position: relative;
}

.tag-input-wrapper {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 0.375rem;
  background: white;
  min-height: 3rem;
}

.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background-color: #007bff;
  color: white;
  border-radius: 1rem;
  font-size: 0.875rem;
}

.remove-tag-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  margin-left: 0.25rem;
  font-size: 0.75rem;
}

.tag-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 1rem;
  min-width: 120px;
}

.tag-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e9ecef;
  border-top: none;
  border-radius: 0 0 0.375rem 0.375rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

.tag-suggestion {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0.75rem;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s;
}

.tag-suggestion:hover {
  background-color: #f8f9fa;
}

.tag-suggestion.create-new {
  color: #007bff;
  font-style: italic;
}

.suggestion-name {
  font-weight: 500;
}

.suggestion-count {
  font-size: 0.875rem;
  color: #6c757d;
}

.suggestion-loading {
  padding: 0.75rem;
  text-align: center;
  color: #6c757d;
}

.tag-input-help {
  font-size: 0.875rem;
  color: #6c757d;
  margin-top: 0.5rem;
}

/* Ask Question Page */
.ask-question-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.ask-question-header {
  margin-bottom: 2rem;
}

.ask-question-header h1 {
  margin-bottom: 0.5rem;
}

.ask-question-header p {
  color: #6c757d;
  font-size: 1.125rem;
}

.ask-question-form {
  background: white;
  padding: 2rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.form-help {
  display: block;
  font-size: 0.875rem;
  color: #6c757d;
  font-weight: normal;
  margin-top: 0.25rem;
}

.ask-question-tips {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 0.5rem;
  border-left: 4px solid #007bff;
}

.ask-question-tips h3 {
  margin-bottom: 1rem;
  color: #333;
}

.ask-question-tips ul {
  margin: 0;
  padding-left: 1.5rem;
}

.ask-question-tips li {
  margin-bottom: 0.5rem;
  color: #6c757d;
}

/* Question Detail */
.question-detail-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
}

.question-detail {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.question-header {
  padding: 2rem 2rem 1rem;
  border-bottom: 1px solid #e9ecef;
}

.question-header h1 {
  margin-bottom: 1rem;
  color: #333;
}

.question-meta-header {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.question-content-wrapper {
  display: flex;
  padding: 2rem;
  gap: 2rem;
}

.vote-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  min-width: 60px;
}

.vote-btn {
  background: none;
  border: 2px solid #e9ecef;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  color: #6c757d;
}

.vote-btn:hover {
  border-color: #007bff;
  color: #007bff;
}

.vote-btn.active {
  border-color: #007bff;
  background-color: #007bff;
  color: white;
}

.vote-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.vote-count {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
}

.question-main-content {
  flex: 1;
}

.question-body {
  margin-bottom: 2rem;
  line-height: 1.6;
}

.question-body h1, .question-body h2, .question-body h3 {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

.question-body p {
  margin-bottom: 1rem;
}

.question-body pre {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 0.375rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.question-body code {
  background: #f8f9fa;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
}

.question-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid #e9ecef;
  background: white;
  color: #6c757d;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn:hover {
  background-color: #f8f9fa;
}

.action-btn.delete {
  color: #dc3545;
  border-color: #dc3545;
}

.action-btn.delete:hover {
  background-color: #dc3545;
  color: white;
}

.question-author-info {
  display: flex;
  justify-content: flex-end;
}

.author-card {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 0.375rem;
  max-width: 300px;
}

.author-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.author-details {
  flex: 1;
}

.author-name {
  font-weight: 600;
  color: #007bff;
  margin-bottom: 0.25rem;
}

.author-reputation {
  font-size: 0.875rem;
  color: #6c757d;
  margin-bottom: 0.25rem;
}

.author-joined {
  font-size: 0.75rem;
  color: #6c757d;
}

/* Answers Section */
.answers-section {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.answers-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.answers-list {
  margin-bottom: 2rem;
}

.answer-item {
  display: flex;
  gap: 2rem;
  padding: 2rem 0;
  border-bottom: 1px solid #e9ecef;
}

.answer-item:last-child {
  border-bottom: none;
}

.answer-item.accepted {
  background: #f8fff8;
  border-left: 4px solid #28a745;
  padding-left: 1.5rem;
  margin-left: -2rem;
  margin-right: -2rem;
  padding-right: 2rem;
}

.answer-vote-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  min-width: 60px;
}

.accept-btn {
  background: none;
  border: 2px solid #28a745;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  color: #28a745;
  margin-top: 0.5rem;
}

.accept-btn:hover {
  background-color: #28a745;
  color: white;
}

.accepted-indicator {
  width: 40px;
  height: 40px;
  background-color: #28a745;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 0.5rem;
}

.accepted-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #28a745;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  margin-bottom: 1rem;
  width: fit-content;
}

.answer-content {
  flex: 1;
}

.answer-body {
  margin-bottom: 2rem;
  line-height: 1.6;
}

.answer-footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.answer-actions {
  display: flex;
  gap: 1rem;
}

.answer-author-info {
  text-align: right;
}

.answer-meta {
  font-size: 0.875rem;
  color: #6c757d;
  margin-bottom: 0.5rem;
}

.no-answers {
  text-align: center;
  padding: 3rem;
  color: #6c757d;
}

.login-prompt {
  text-align: center;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 0.375rem;
  margin-top: 2rem;
}

.login-prompt a {
  color: #007bff;
  text-decoration: none;
  font-weight: 500;
}

.login-prompt a:hover {
  text-decoration: underline;
}

/* Answer Form */
.answer-form-container {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e9ecef;
}

.answer-form {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.answer-guidelines {
  background: #e7f3ff;
  padding: 1.5rem;
  border-radius: 0.5rem;
  border-left: 4px solid #007bff;
}

.answer-guidelines h4 {
  margin-bottom: 1rem;
  color: #333;
}

.answer-guidelines ul {
  margin: 0;
  padding-left: 1.5rem;
}

.answer-guidelines li {
  margin-bottom: 0.5rem;
  color: #6c757d;
}

/* Tags Page */
.tags-page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.tags-header {
  margin-bottom: 2rem;
}

.tags-header h1 {
  margin-bottom: 1rem;
}

.tags-header p {
  color: #6c757d;
  font-size: 1.125rem;
  line-height: 1.6;
}

.tags-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  gap: 1rem;
}

.search-input-wrapper {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
}

.search-input-wrapper .search-input {
  padding-left: 3rem;
}

.tags-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.tag-card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  transition: transform 0.2s, box-shadow 0.2s;
}

.tag-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.tag-name {
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: #007bff;
  color: white;
  text-decoration: none;
  border-radius: 1rem;
  font-weight: 500;
  margin-bottom: 1rem;
  transition: opacity 0.2s;
}

.tag-name:hover {
  opacity: 0.8;
  color: white;
}

.tag-description {
  color: #6c757d;
  line-height: 1.6;
  margin-bottom: 1rem;
  min-height: 3rem;
}

.tag-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
  color: #6c757d;
}

.question-count {
  font-weight: 500;
}

.created-by {
  font-style: italic;
}

.no-tags {
  text-align: center;
  padding: 3rem;
  color: #6c757d;
}

/* Profile Page */
.profile-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
}

.profile-header {
  display: flex;
  gap: 2rem;
  background: white;
  padding: 2rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.profile-avatar .avatar-large {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-initials {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  font-weight: 600;
  text-transform: uppercase;
}

.profile-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.profile-info {
  flex: 1;
}

.profile-info h1 {
  margin-bottom: 1rem;
}

.profile-meta {
  display: flex;
  gap: 2rem;
  margin-bottom: 1rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6c757d;
}

.reputation {
  color: #007bff;
  font-weight: 600;
}

.profile-bio {
  color: #6c757d;
  line-height: 1.6;
}

.profile-tabs {
  display: flex;
  background: white;
  border-radius: 0.5rem 0.5rem 0 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-btn {
  padding: 1rem 2rem;
  border: none;
  background: none;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.2s;
  border-bottom: 3px solid transparent;
}

.tab-btn:hover {
  background-color: #f8f9fa;
}

.tab-btn.active {
  color: #007bff;
  border-bottom-color: #007bff;
}

.profile-content {
  background: white;
  padding: 2rem;
  border-radius: 0 0 0.5rem 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  text-align: center;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 0.5rem;
  border: 1px solid #e9ecef;
}

.stat-icon {
  font-size: 2rem;
  color: #007bff;
  margin-bottom: 1rem;
}

.stat-number {
  font-size: 2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: #6c757d;
  font-weight: 500;
}

.recent-activity h3 {
  margin-bottom: 1rem;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  padding: 1rem;
  border: 1px solid #e9ecef;
  border-radius: 0.375rem;
  transition: background-color 0.2s;
}

.activity-item:hover {
  background-color: #f8f9fa;
}

.activity-content h4 {
  margin-bottom: 0.5rem;
}

.activity-content h4 a {
  color: #007bff;
  text-decoration: none;
}

.activity-content h4 a:hover {
  text-decoration: underline;
}

.activity-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.questions-list .question-item {
  display: flex;
  gap: 1.5rem;
  padding: 1.5rem 0;
  border-bottom: 1px solid #e9ecef;
}

.questions-list .question-item:last-child {
  border-bottom: none;
}

.questions-list .question-stats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 80px;
}

.questions-list .question-content {
  flex: 1;
}

.questions-list .question-content h4 {
  margin-bottom: 1rem;
}

.questions-list .question-content h4 a {
  color: #007bff;
  text-decoration: none;
}

.questions-list .question-content h4 a:hover {
  text-decoration: underline;
}

.questions-list .question-tags {
  margin-bottom: 0.5rem;
}

.questions-list .question-date {
  font-size: 0.875rem;
  color: #6c757d;
}

.no-content {
  text-align: center;
  padding: 3rem;
  color: #6c757d;
}

/* Notification Dropdown */
.notification-dropdown {
  position: relative;
}

.notification-dropdown-content {
  position: absolute;
  right: 0;
  top: 100%;
  width: 500px;
  max-height: 600px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 0.75rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  overflow: hidden;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
}

.notification-header h3 {
  margin: 0;
  font-size: 1.125rem;
}

.mark-all-read-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s;
}

.mark-all-read-btn:hover {
  background-color: #0056b3;
}

.notification-list {
  max-height: 400px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s;
  cursor: pointer;
}

.notification-item:hover {
  background-color: #f8f9fa;
}

.notification-item.unread {
  background-color: #e7f3ff;
  border-left: 4px solid #007bff;
}

.notification-avatar {
  flex-shrink: 0;
}

.notification-content {
  flex: 1;
  margin-right: 1rem;
}

.notification-title {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
}

.notification-message {
  color: #6c757d;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.notification-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.75rem;
  color: #6c757d;
}

.notification-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.notification-actions .action-btn {
  padding: 0.25rem;
  border: none;
  background: none;
  color: #6c757d;
  cursor: pointer;
  border-radius: 0.25rem;
  transition: all 0.2s;
}

.notification-actions .action-btn:hover {
  background-color: #e9ecef;
}

.notification-actions .action-btn.delete:hover {
  background-color: #dc3545;
  color: white;
}

.notification-link {
  display: block;
  margin-top: 0.5rem;
  color: #007bff;
  text-decoration: none;
  font-size: 0.875rem;
}

.notification-link:hover {
  text-decoration: underline;
}

.notification-loading {
  padding: 2rem;
  text-align: center;
  color: #6c757d;
}

.no-notifications {
  padding: 2rem;
  text-align: center;
  color: #6c757d;
}

.no-notifications .empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-notifications h4 {
  margin-bottom: 0.5rem;
  color: #495057;
}

.no-notifications p {
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.notification-tips {
  text-align: left;
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 0.5rem;
  margin-top: 1rem;
}

.notification-tips h5 {
  margin-bottom: 0.5rem;
  color: #495057;
  font-size: 0.875rem;
}

.notification-tips ul {
  margin: 0;
  padding-left: 1.5rem;
  font-size: 0.875rem;
}

.notification-tips li {
  margin-bottom: 0.25rem;
}

.notification-loading {
  padding: 2rem;
  text-align: center;
  color: #6c757d;
}

.loading-spinner {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  animation: spin 1s linear infinite;
}

.notification-footer {
  padding: 1rem;
  text-align: center;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
}

.notification-footer a {
  color: #007bff;
  text-decoration: none;
  font-weight: 500;
}

.notification-footer a:hover {
  text-decoration: underline;
}

/* Edit Profile */
.edit-profile-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
}

.edit-profile-header {
  margin-bottom: 2rem;
}

.edit-profile-header h1 {
  margin-bottom: 0.5rem;
}

.edit-profile-header p {
  color: #6c757d;
  font-size: 1.125rem;
}

.edit-profile-form {
  background: white;
  padding: 2rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.profile-avatar-section {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #e9ecef;
}

.current-avatar {
  flex-shrink: 0;
}

.avatar-preview {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: 600;
}

.avatar-upload {
  flex: 1;
}

.upload-help {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.char-count {
  text-align: right;
  font-size: 0.875rem;
  color: #6c757d;
  margin-top: 0.25rem;
}

/* Avatar Component */
.avatar {
  border-radius: 50%;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.2s;
}

.avatar:hover {
  transform: scale(1.05);
}

.avatar-small {
  width: 24px;
  height: 24px;
  font-size: 0.75rem;
}

.avatar-medium {
  width: 40px;
  height: 40px;
  font-size: 1rem;
}

.avatar-large {
  width: 80px;
  height: 80px;
  font-size: 1.5rem;
}

.avatar-xlarge {
  width: 120px;
  height: 120px;
  font-size: 2.5rem;
}

.avatar-initials {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  text-transform: uppercase;
}

/* Markdown Content */
.markdown-content {
  line-height: 1.6;
  color: #333;
}

.markdown-content h1 {
  font-size: 2rem;
  margin: 1.5rem 0 1rem 0;
  color: #333;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 0.5rem;
}

.markdown-content h2 {
  font-size: 1.5rem;
  margin: 1.25rem 0 0.75rem 0;
  color: #333;
}

.markdown-content h3 {
  font-size: 1.25rem;
  margin: 1rem 0 0.5rem 0;
  color: #333;
}

.markdown-content strong {
  font-weight: 600;
  color: #333;
}

.markdown-content em {
  font-style: italic;
  color: #555;
}

.markdown-content code {
  background: #f8f9fa;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', Monaco, Consolas, monospace;
  font-size: 0.875em;
  color: #e83e8c;
  border: 1px solid #e9ecef;
}

.markdown-content pre {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 0.375rem;
  border: 1px solid #e9ecef;
  overflow-x: auto;
  margin: 1rem 0;
}

.markdown-content pre code {
  background: none;
  padding: 0;
  border: none;
  color: #333;
  font-size: 0.875rem;
}

.markdown-content a {
  color: #007bff;
  text-decoration: none;
}

.markdown-content a:hover {
  text-decoration: underline;
}

.markdown-content ul {
  margin: 1rem 0;
  padding-left: 2rem;
}

.markdown-content li {
  margin: 0.25rem 0;
  list-style-type: disc;
}

.markdown-content p {
  margin: 1rem 0;
}

.markdown-content br {
  line-height: 1.6;
}

/* Edit Modals */
.edit-question-modal,
.edit-answer-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  max-width: 800px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e9ecef;
}

.modal-header h2 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6c757d;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #f8f9fa;
  color: #333;
}

.edit-question-form,
.edit-answer-form {
  padding: 1.5rem;
}

/* Profile Question/Answer Headers */
.question-header,
.answer-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.question-header h4,
.answer-header h4 {
  flex: 1;
  margin: 0;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  min-width: auto;
}

.answer-preview {
  color: #6c757d;
  font-size: 0.875rem;
  line-height: 1.4;
  margin-bottom: 0.5rem;
}

.answer-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.answer-meta .accepted {
  color: #28a745;
  font-weight: 600;
}

.answer-item {
  padding: 1rem;
  border: 1px solid #e9ecef;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
  background: white;
}

/* Image Upload Styles */
.image-upload-container {
  margin: 1rem 0;
}

.image-upload-area {
  border: 2px dashed #ced4da;
  border-radius: 0.5rem;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.image-upload-area:hover {
  border-color: #007bff;
  background: #e3f2fd;
}

.image-upload-label {
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.upload-icon {
  font-size: 3rem;
  color: #6c757d;
}

.upload-text {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.upload-text span {
  font-size: 1.125rem;
  color: #333;
  font-weight: 500;
}

.upload-text small {
  color: #6c757d;
  font-size: 0.875rem;
}

.image-preview {
  position: relative;
  display: inline-block;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-preview img {
  max-width: 100%;
  max-height: 300px;
  width: auto;
  height: auto;
  display: block;
}

.remove-image-btn {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.25rem;
  transition: background 0.2s;
}

.remove-image-btn:hover {
  background: rgba(220, 53, 69, 0.8);
}

/* Question Image Display */
.question-image {
  margin: 1rem 0;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.question-image img {
  width: 100%;
  max-height: 200px;
  object-fit: cover;
  display: block;
}

.question-image-detail {
  margin: 1.5rem 0;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  background: white;
  padding: 0.5rem;
}

.question-image-detail img {
  width: 100%;
  max-height: 400px;
  object-fit: contain;
  display: block;
  border-radius: 0.5rem;
}

/* Compact Design Improvements */
.main-content {
  padding: 1.5rem 0;
}

.container {
  max-width: 1400px;
  padding: 0 1.5rem;
}

/* Form Improvements */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.95rem;
}

.form-help {
  display: block;
  font-size: 0.875rem;
  color: #6c757d;
  font-weight: 400;
  margin-top: 0.25rem;
}

input[type="text"],
input[type="email"],
input[type="password"],
textarea {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #e9ecef;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-container {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-menu {
    flex-direction: column;
    gap: 1rem;
  }

  .posts-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .posts-grid {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .post-actions {
    flex-direction: column;
  }

  .comment-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .search-filters {
    flex-direction: column;
    align-items: stretch;
  }

  .search-form {
    max-width: none;
  }

  .sort-options {
    justify-content: center;
    flex-wrap: wrap;
  }

  .question-card {
    flex-direction: column;
  }

  .question-stats {
    flex-direction: row;
    margin-right: 0;
    margin-bottom: 1rem;
    justify-content: space-around;
  }

  .question-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Enhanced Rich Text Editor */
.editor-toolbar {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-bottom: none;
  border-radius: 0.5rem 0.5rem 0 0;
  flex-wrap: wrap;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.editor-toolbar button {
  padding: 0.5rem;
  border: none;
  background: transparent;
  border-radius: 0.25rem;
  cursor: pointer;
  color: #495057;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
}

.editor-toolbar button:hover {
  background: #e9ecef;
  color: #212529;
}

.editor-toolbar button.active {
  background: #007bff;
  color: white;
}

.toolbar-divider {
  width: 1px;
  height: 24px;
  background: #dee2e6;
  margin: 0 0.25rem;
}

.emoji-picker-container {
  position: relative;
}

.emoji-picker {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;
  padding: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 0.25rem;
  min-width: 200px;
}

.emoji-btn {
  padding: 0.25rem;
  border: none;
  background: transparent;
  border-radius: 0.25rem;
  cursor: pointer;
  font-size: 1.2rem;
  transition: background 0.2s;
}

.emoji-btn:hover {
  background: #f8f9fa;
}

.rich-text-editor textarea {
  border-radius: 0 0 0.5rem 0.5rem;
  border-top: none;
}

.editor-preview {
  border-radius: 0 0 0.5rem 0.5rem;
  border-top: none;
}

/* Rich text content styling */
.editor-preview h2 {
  color: #2c3e50;
  margin: 1rem 0 0.5rem 0;
  font-size: 1.5rem;
}

.editor-preview code {
  background: #f8f9fa;
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
  color: #e83e8c;
}

.editor-preview a {
  color: #007bff;
  text-decoration: none;
}

.editor-preview a:hover {
  text-decoration: underline;
}

.editor-preview img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 0.5rem 0;
}

.editor-preview li {
  margin: 0.25rem 0;
}

.editor-preview del {
  color: #6c757d;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Admin Dashboard */
.admin-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.admin-header {
  text-align: center;
  margin-bottom: 2rem;
}

.admin-header h1 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.admin-header p {
  color: #6c757d;
  font-size: 1.125rem;
}

.access-denied {
  text-align: center;
  padding: 3rem;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.admin-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: white;
  padding: 1.5rem;
  border-radius: 0.75rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 2.5rem;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 1rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.stat-label {
  color: #6c757d;
  font-weight: 500;
}

.admin-tabs {
  display: flex;
  background: white;
  border-radius: 0.5rem 0.5rem 0 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.admin-tabs .tab-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  border: none;
  background: none;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.2s;
  border-bottom: 3px solid transparent;
  font-weight: 500;
}

.admin-tabs .tab-btn:hover {
  background: #f8f9fa;
}

.admin-tabs .tab-btn.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background: #f8f9fa;
}

.admin-content {
  background: white;
  padding: 2rem;
  border-radius: 0 0 0.5rem 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  min-height: 400px;
}

.users-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.user-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;
  transition: background 0.2s;
}

.user-item:hover {
  background: #f8f9fa;
}

.user-info {
  flex: 1;
}

.user-name {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.user-email {
  color: #6c757d;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.user-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.75rem;
  color: #6c757d;
}

.user-actions {
  display: flex;
  gap: 0.5rem;
}

.broadcast-form {
  max-width: 600px;
}

.broadcast-form textarea {
  width: 100%;
  margin-bottom: 1rem;
  min-height: 150px;
  resize: vertical;
}

.reports-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.report-card {
  padding: 1.5rem;
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;
  text-align: center;
  transition: transform 0.2s;
}

.report-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.report-card h4 {
  margin-bottom: 1rem;
  color: #2c3e50;
}

.report-card p {
  color: #6c757d;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.btn-outline {
  background: transparent;
  border: 2px solid #667eea;
  color: #667eea;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-outline:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

/* Answer Owner Actions */
.owner-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.accept-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.accept-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

.reject-btn {
  background: linear-gradient(135deg, #dc3545, #e74c3c);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.reject-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
}

/* Answer Status Indicators */
.answer-status {
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: inline-block;
}

.answer-status.pending {
  background: #fff3cd;
  color: #856404;
}

.answer-status.accepted {
  background: #d4edda;
  color: #155724;
}

.answer-status.rejected {
  background: #f8d7da;
  color: #721c24;
}
