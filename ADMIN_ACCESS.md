# How to Access Admin Dashboard

## Method 1: Database Update (Recommended)

1. **Connect to your MongoDB database** using MongoDB Compass, Studio 3T, or command line
2. **Find your user** in the `users` collection
3. **Update the user's role** to 'admin':
   ```javascript
   db.users.updateOne(
     { email: "<EMAIL>" },
     { $set: { role: "admin" } }
   )
   ```
4. **Logout and login again** to refresh your session
5. **Click on your profile dropdown** in the navbar
6. **You should see "Admin Dashboard" option**

## Method 2: Create Admin User via Registration

1. **Register a new account** with email: `<EMAIL>`
2. **The system will automatically assign admin role** to this email
3. **Login with the admin account**
4. **Access Admin Dashboard** from profile dropdown

## Admin Features Available:

### 📊 **Dashboard Overview**
- Total users count
- Total questions count
- Platform statistics

### 👥 **User Management**
- View all users
- Ban/unban users
- Monitor user activity
- View user registration dates and roles

### 📢 **Broadcast Messages**
- Send platform-wide announcements
- Notify all users about updates
- Send maintenance alerts

### 📈 **Reports & Analytics**
- Download user activity reports
- Export content statistics
- Generate platform analytics

## Admin Dashboard URL:
`http://localhost:5174/admin`

## Troubleshooting:

### If you don't see Admin Dashboard option:
1. Check your user role in database: `db.users.findOne({email: "your-email"})`
2. Make sure role is exactly "admin" (lowercase)
3. Clear browser cache and login again
4. Check browser console for any errors

### If you get "Access Denied":
1. Verify your role is set to "admin" in database
2. Make sure you're logged in
3. Try refreshing the page

## Default Admin Credentials (if needed):
- Email: `<EMAIL>`
- Password: `admin123` (change after first login)

---

**Note**: Admin access gives full control over the platform. Use responsibly!
