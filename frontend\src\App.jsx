import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { Provider } from 'react-redux'
import { store } from './redux/store'
import './styles/App.css'

// Components
import Navbar from './components/layout/Navbar'
import Home from './components/questions/Home'
import Login from './components/auth/Login'
import Signup from './components/auth/Signup'
import ProtectedRoute from './components/auth/ProtectedRoute'
import AskQuestion from './components/questions/AskQuestion'
import QuestionDetail from './components/questions/QuestionDetail'
import TagsPage from './components/tags/TagsPage'
import Profile from './components/user/Profile'
import EditProfile from './components/user/EditProfile'

function App() {
  return (
    <Provider store={store}>
      <Router>
        <div className="app">
          <Navbar />
          <main className="main-content">
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/login" element={<Login />} />
              <Route path="/signup" element={<Signup />} />
              <Route path="/tags" element={<TagsPage />} />
              <Route path="/questions/:id" element={<QuestionDetail />} />

              {/* Protected Routes */}
              <Route
                path="/ask"
                element={
                  <ProtectedRoute>
                    <AskQuestion />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/profile"
                element={
                  <ProtectedRoute>
                    <Profile />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/profile/edit"
                element={
                  <ProtectedRoute>
                    <EditProfile />
                  </ProtectedRoute>
                }
              />

              {/* Admin Routes */}
              <Route
                path="/admin"
                element={
                  <ProtectedRoute adminOnly>
                    <div>Admin Dashboard (Coming Soon)</div>
                  </ProtectedRoute>
                }
              />

              {/* 404 Route */}
              <Route path="*" element={<div>Page Not Found</div>} />
            </Routes>
          </main>
        </div>
      </Router>
    </Provider>
  )
}

export default App
